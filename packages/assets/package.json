{"name": "@platform/assets", "type": "module", "version": "0.0.0", "license": "MIT", "main": "./index.js", "types": "./index.d.ts", "scripts": {"generate-types": "pnpm ts-node scripts/generate-types.ts", "build": "nx run assets:build", "lint": "nx run assets:lint"}, "dependencies": {"@types/node": "^20.11.0", "@types/react": "~19.0.14", "react": "19.0.0", "tslib": "^2.3.0"}, "devDependencies": {"@nx/js": "20.7.0", "ts-node": "^10.9.2", "typescript": "~5.7.2"}}