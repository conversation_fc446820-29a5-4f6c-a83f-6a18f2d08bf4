import { AnimatePresence, motion } from "motion/react";
import { memo, useCallback, useEffect, useMemo, useState } from "react";

function LocationSearchResultsComponent({ primaryColor, teamId, isVisible = false, onLocationSelect }) {
  console.log("LocationSearchResults: Component initialized with teamId:", teamId, "primaryColor:", primaryColor);

  const [locations, setLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const isBrowser = typeof window !== "undefined" && typeof sessionStorage !== "undefined";

  // Safely access sessionStorage - memoized to prevent recreation on every render
  const safeSessionStorage = useMemo(
    () => ({
      getItem: (key) => {
        try {
          return isBrowser ? sessionStorage.getItem(key) : null;
        } catch (e) {
          console.warn("Error accessing sessionStorage:", e);
          return null;
        }
      },
      setItem: (key, value) => {
        try {
          if (isBrowser) sessionStorage.setItem(key, value);
        } catch (e) {
          console.warn("Error writing to sessionStorage:", e);
        }
      },
    }),
    [isBrowser],
  );

  // Handler for when a location is selected
  const handleLocationClick = useCallback(
    (location) => {
      console.log("LocationSearchResults: Location selected:", location);

      // Set the global variable for the search button to use
      if (typeof window !== "undefined") {
        window.__selectedLocation = location;
        console.log("Set global location variable:", window.__selectedLocation);
      }

      // Call the parent callback if provided
      if (onLocationSelect) {
        onLocationSelect(location);
      }
    },
    [onLocationSelect],
  );

  useEffect(() => {
    console.log("LocationSearchResults: useEffect triggered with teamId:", teamId);

    // Function to fetch locations and extract unique neighborhoods
    const fetchLocations = async () => {
      console.log("LocationSearchResults: fetchLocations called with teamId:", teamId);

      // Skip if no teamId is provided
      if (!teamId) {
        console.log("LocationSearchResults: No teamId provided, skipping fetch");
        return;
      }

      // Check if we have cached locations for this team
      const sessionStorageKey = `teamLocationStrings_${teamId}`;
      const cachedLocations = safeSessionStorage.getItem(sessionStorageKey);

      if (cachedLocations) {
        try {
          const parsedLocations = JSON.parse(cachedLocations);

          // Validate the cached data structure - must be array of objects with cityName and neighborhoods
          if (
            parsedLocations &&
            parsedLocations.length > 0 &&
            Array.isArray(parsedLocations) &&
            parsedLocations.every(
              (city) =>
                city &&
                typeof city === "object" &&
                "cityName" in city &&
                "neighborhoods" in city &&
                Array.isArray(city.neighborhoods),
            )
          ) {
            console.log("LocationSearchResults: Using cached locations:", parsedLocations.length, "cities");
            setLocations(parsedLocations);
            return;
          }
          console.warn("LocationSearchResults: Cached data format is invalid, clearing cache");
          safeSessionStorage.setItem(sessionStorageKey, ""); // Clear invalid cache
        } catch (err) {
          console.error("Error parsing cached locations:", err);
          // Continue with fetching if parsing fails
        }
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get the current URL to construct an absolute URL
        const currentUrl = new URL(window.location.href);
        const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
        const apiUrl = `${baseUrl}/api/get-team-locations?teamId=${teamId}`;

        console.log(`LocationSearchResults: Fetching locations from: ${apiUrl}`);

        const response = await fetch(apiUrl);
        console.log("LocationSearchResults: API response status:", response.status, response.statusText);

        // Check if the response is ok
        if (!response.ok) {
          const errorText = await response.text();
          console.error("LocationSearchResults: API error response:", errorText);
          throw new Error(`Failed to fetch locations: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.locations || !Array.isArray(data.locations)) {
          console.warn("LocationSearchResults: No locations found or invalid response format");
          setLocations([]);
          setIsLoading(false);
          return;
        }

        // Group neighborhoods by city
        const groupedLocations = data.locations
          .filter((city) => city.neighborhoods && Array.isArray(city.neighborhoods) && city.neighborhoods.length > 0)
          .map((city) => ({
            cityName: city.name,
            neighborhoods: city.neighborhoods.map((neighborhood) => neighborhood.name),
          }));

        console.log("LocationSearchResults: Processed locations:", groupedLocations.length, "cities");

        // Update state with grouped locations
        setLocations(groupedLocations);

        // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem(sessionStorageKey, JSON.stringify(groupedLocations));
      } catch (err) {
        console.error("LocationSearchResults: Error fetching locations:", err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    // Only fetch if component is visible
    if (isVisible) {
      fetchLocations();
    }
  }, [teamId, isVisible, safeSessionStorage]);

  // Animation variants for Motion (faster animation as requested)
  const dropdownVariants = {
    hidden: {
      height: 0,
      opacity: 0,
      transition: {
        duration: 0.2,
        ease: "easeOut",
      },
    },
    visible: {
      height: "auto",
      opacity: 1,
      transition: {
        duration: 0.2,
        ease: "easeOut",
      },
    },
  };

  // Function to render the appropriate content based on state
  function renderContent() {
    // Render loading state
    if (isLoading) {
      return (
        <div className="p-4 text-center text-gray-600">
          <div className="animate-pulse">Carregando localizações...</div>
        </div>
      );
    }

    // Render error state
    if (error) {
      return (
        <div className="p-4 text-center text-red-600">
          <div>Erro ao carregar localizações</div>
          <div className="mt-1 text-gray-500 text-sm">{error}</div>
        </div>
      );
    }

    // Render locations grouped by city
    return (
      <div className="max-h-64 overflow-y-auto">
        {locations.length > 0 ? (
          <div className="space-y-3 px-4 py-3">
            {locations.map((city) => (
              <div key={city.cityName} className="space-y-2">
                <h3 className="font-medium text-gray-900 text-sm">{city.cityName}:</h3>
                <div className="flex flex-wrap gap-2">
                  {city.neighborhoods &&
                    Array.isArray(city.neighborhoods) &&
                    city.neighborhoods.map((neighborhood) => {
                      const locationKey = `${neighborhood}, ${city.cityName}`;
                      return (
                        <button
                          key={locationKey}
                          type="button"
                          className="cursor-pointer rounded-full border border-gray-300 bg-gray-50 px-3 py-1.5 font-medium text-gray-700 text-xs transition-all duration-150 ease-in-out hover:border-gray-400 hover:bg-gray-100 focus:border-gray-400 focus:bg-gray-100 focus:outline-none"
                          onClick={() => handleLocationClick(locationKey)}
                          style={{
                            "--hover-color": primaryColor ? `${primaryColor}15` : "#f3f4f6",
                          }}
                          onMouseEnter={(e) => {
                            if (primaryColor) {
                              e.target.style.backgroundColor = `${primaryColor}15`;
                              e.target.style.borderColor = primaryColor;
                            }
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.backgroundColor = "";
                            e.target.style.borderColor = "";
                          }}
                        >
                          {neighborhood}
                        </button>
                      );
                    })}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-4 text-center text-gray-600">
            <div className="flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="mr-2 h-5 w-5 text-gray-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                  clipRule="evenodd"
                />
              </svg>
              Nenhuma localização encontrada
            </div>
          </div>
        )}
      </div>
    );
  }

  // Render the component with AnimatePresence to handle both enter and exit animations
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial="hidden"
          animate="visible"
          exit="hidden"
          variants={dropdownVariants}
          className="absolute top-[50%] left-0 w-full overflow-hidden rounded-b-4xl border border-gray-300 bg-white/80 shadow-xl backdrop-blur-sm"
        >
          <div className="pt-12">{renderContent()}</div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const LocationSearchResults = memo(LocationSearchResultsComponent);
