import { memo, useCallback, useEffect, useRef, useState, useMemo } from "react";

function PropertyLocationSelectComponent({ primaryColor, teamId }) {
  console.log("PropertyLocationSelect: Component initialized with teamId:", teamId, "primaryColor:", primaryColor);

  // Add client-side detection log
  if (typeof window !== "undefined") {
    console.log("PropertyLocationSelect: Running on CLIENT SIDE");
  } else {
    console.log("PropertyLocationSelect: Running on SERVER SIDE");
  }

  const [locations, setLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [_selectedLocation, setSelectedLocation] = useState(null);
  const [useNativeSelect, setUseNativeSelect] = useState(false);
  const selectRef = useRef(null);

  // Handler for when a location is selected
  const handleLocationChange = useCallback((value) => {
    console.log("PropertyLocationSelect: Location selected:", value);
    setSelectedLocation(value);

    // Set the global variable for the search button to use
    if (typeof window !== "undefined") {
      window.__selectedLocation = value;
      console.log("Set global location variable:", window.__selectedLocation);
    }
  }, []);

  // Check if we're in a browser environment where sessionStorage is available
  const isBrowser = typeof window !== "undefined" && typeof sessionStorage !== "undefined";

  // Safely access sessionStorage - memoized to prevent recreation on every render
  const safeSessionStorage = useMemo(() => ({
    getItem: (key) => {
      try {
        return isBrowser ? sessionStorage.getItem(key) : null;
      } catch (e) {
        console.warn("Error accessing sessionStorage:", e);
        return null;
      }
    },
    setItem: (key, value) => {
      try {
        if (isBrowser) sessionStorage.setItem(key, value);
      } catch (e) {
        console.warn("Error writing to sessionStorage:", e);
      }
    },
  }), [isBrowser]);

  useEffect(() => {
    console.log("PropertyLocationSelect: useEffect triggered with teamId:", teamId, "typeof window:", typeof window);

    // Function to fetch locations and extract unique neighborhoods
    const fetchLocations = async () => {
      console.log("PropertyLocationSelect: fetchLocations called with teamId:", teamId);

      // Skip if no teamId is provided
      if (!teamId) {
        console.log("PropertyLocationSelect: No teamId provided, skipping fetch");
        return;
      }

      // Check if we have cached locations for this team
      const sessionStorageKey = `teamLocations_${teamId}`;
      let cachedLocations;

      // Use our safe wrapper to access sessionStorage
      cachedLocations = safeSessionStorage.getItem(sessionStorageKey);

      if (cachedLocations) {
        try {
          const parsedLocations = JSON.parse(cachedLocations);

          // Only use cached locations if they're not empty
          if (parsedLocations && parsedLocations.length > 0) {
            setLocations(parsedLocations);
            return;
          }
        } catch (err) {
          console.error("Error parsing cached locations:", err);
          // Continue with fetching if parsing fails
        }
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get the current URL to construct an absolute URL
        const currentUrl = new URL(window.location.href);
        const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
        const apiUrl = `${baseUrl}/api/get-team-locations?teamId=${teamId}`;

        console.log(`PropertyLocationSelect: Fetching locations from: ${apiUrl}`);

        const response = await fetch(apiUrl);
        console.log("PropertyLocationSelect: API response status:", response.status, response.statusText);

        // Check if the response is ok
        if (!response.ok) {
          const errorText = await response.text();
          console.error("PropertyLocationSelect: API error response:", errorText);
          throw new Error(`Failed to fetch locations: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.locations || !Array.isArray(data.locations)) {
          console.warn("PropertyLocationSelect: No locations found or invalid response format");
          setLocations([]);
          setIsLoading(false);
          return;
        }

        // Flatten neighborhoods from all cities into a single array
        const allNeighborhoods = [];
        data.locations.forEach((city) => {
          if (city.neighborhoods && Array.isArray(city.neighborhoods)) {
            city.neighborhoods.forEach((neighborhood) => {
              allNeighborhoods.push(`${neighborhood.name}, ${city.name}`);
            });
          }
        });

        console.log("PropertyLocationSelect: Processed locations:", allNeighborhoods.length);

        // Update state with the flattened neighborhoods
        setLocations(allNeighborhoods);

        // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem(sessionStorageKey, JSON.stringify(allNeighborhoods));
      } catch (err) {
        console.error("PropertyLocationSelect: Error fetching locations:", err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    // Call the fetch function
    fetchLocations();
  }, [teamId, safeSessionStorage.getItem, // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem]); // Only depend on teamId, not the sessionStorage functions

  // Initialize Preline select after component mounts and data changes
  useEffect(() => {
    if (typeof window === "undefined" || !selectRef.current || useNativeSelect) return;

    let retryCount = 0;
    const maxRetries = 20; // Try for 2 seconds
    let cleanupFunction = null;

    // Wait for Preline to be available
    const initializeSelect = () => {
      if (window.HSSelect) {
        try {
          const selectElement = selectRef.current;
          if (selectElement) {
            // Destroy existing instance if it exists
            const existingInstance = window.HSSelect.getInstance(selectElement);
            if (existingInstance) {
              existingInstance.destroy();
            }

            // Force a small delay to ensure DOM is updated
            setTimeout(() => {
              // Initialize the select
              window.HSSelect.autoInit();

              // Add event listener for selection changes
              const handleChange = (e) => {
                const selectedValue = e.detail.payload;
                if (
                  selectedValue &&
                  selectedValue !== "loading" &&
                  selectedValue !== "error" &&
                  selectedValue !== "none"
                ) {
                  handleLocationChange(selectedValue);
                }
              };

              selectElement.addEventListener("change.hs.select", handleChange);

              cleanupFunction = () => {
                selectElement.removeEventListener("change.hs.select", handleChange);
                const instance = window.HSSelect.getInstance(selectElement);
                if (instance) {
                  instance.destroy();
                }
              };
            }, 10);
          }
        } catch (error) {
          console.warn("Error initializing Preline select:", error);
          setUseNativeSelect(true);
        }
      } else {
        retryCount++;
        if (retryCount < maxRetries) {
          // Retry after a short delay if Preline isn't loaded yet
          setTimeout(initializeSelect, 100);
        } else {
          console.warn("Preline UI not available, falling back to native select");
          setUseNativeSelect(true);
        }
      }
    };

    // Start initialization
    initializeSelect();

    // Cleanup function
    return () => {
      if (cleanupFunction) {
        cleanupFunction();
      }
    };
  }, [useNativeSelect, handleLocationChange]); // Removed handleLocationChange to prevent unnecessary re-initializations

  // Handle native select change
  const _handleNativeSelectChange = (e) => {
    const value = e.target.value;
    if (value && value !== "loading" && value !== "error" && value !== "none") {
      handleLocationChange(value);
    }
  };

  // Render select options based on state
  const renderSelectOptions = () => {
    console.log("PropertyLocationSelect: renderSelectOptions called with:", {
      isLoading,
      error,
      locationsType: typeof locations,
      locationsLength: locations?.length,
      locationsContent: locations,
    });

    if (isLoading) {
      return (
        <option value="loading" disabled>
          Carregando...
        </option>
      );
    }

    if (error) {
      return (
        <option value="error" disabled>
          Erro ao carregar localizações
        </option>
      );
    }

    if (locations && locations.length > 0) {
      return locations
        .map((location, index) => {
          // Add safety check to ensure location is a string
          if (typeof location !== "string") {
            console.error("PropertyLocationSelect: Invalid location object found:", location);
            return null; // Skip invalid entries
          }

          return (
            <option key={location || `location-${index}`} value={location}>
              {location}
            </option>
          );
        })
        .filter(Boolean); // Remove null entries
    }

    // Always show at least one selectable option
    return <option value="none">Nenhuma localização encontrada</option>;
  };

  return (
    <div className="relative flex flex-1">
      <div className="pointer-events-none absolute inset-y-0 left-3 z-10 flex w-full items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 text-gray-400"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
            clipRule="evenodd"
          />
        </svg>
      </div>

      <select
        ref={selectRef}
        data-hs-select={JSON.stringify({
          placeholder: "Localização",
          toggleTag: '<button type="button" aria-expanded="false"></button>',
          toggleClasses:
            "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative w-full pl-10 pr-4 py-4 flex gap-x-2 text-nowrap cursor-pointer bg-white text-start text-sm focus:outline-none focus:ring-1 focus:border-transparent shadow-xl md:shadow-none",
          dropdownClasses:
            "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
          optionClasses:
            "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
          optionTemplate:
            '<div class="flex justify-between items-center w-full"><span data-title></span><span class="hidden hs-selected:block"><svg class="shrink-0 size-3.5 text-blue-600 dark:text-blue-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"/></svg></span></div>',
          extraMarkup:
            '<div class="absolute top-1/2 end-3 -translate-y-1/2"><svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m7 15 5 5 5-5"/><path d="m7 9 5-5 5 5"/></svg></div>',
        })}
        className="hidden"
        style={{ "--tw-ring-color": primaryColor }}
      >
        <option value="">Localização</option>
        {renderSelectOptions()}
      </select>
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const PropertyLocationSelect = memo(PropertyLocationSelectComponent);
