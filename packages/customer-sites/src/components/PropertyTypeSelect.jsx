import Home11Icon from "@platform/assets/icons/duotone/home-11.svg?react";
import { memo, useEffect, useState } from "react";
import { Select } from "./ui/select.jsx";

// Property type mapping from codes to display names
const PROPERTY_TYPE_VALUE_LABEL_MAP = {
  apartment: "Apartamento",
  house: "Casa",
  condominium_house: "Casa de Condomínio",
  penthouse: "Cobertura",
  flat: "Flat",
  studio: "Kitnet / Conjugado",
  lot: "Lote / Terreno",
  townhouse: "Sobrado",
  residential_building: "Edifício Residencial",
  rural_property: "Fazenda / Sítios / Chácaras",
  medical_office: "Consultório",
  warehouse: "Galpão / Depósito / Armazém",
  commercial_property: "Imóvel Comercial",
  commercial_lot: "Lote / Terreno Comercial",
  store: "Ponto Comercial / Loja / Box",
  office: "Sala/Conjunto",
  commercial_building: "Prédio/casa comercial",
};

// Check if we're in a browser environment where sessionStorage is available
const isBrowser = typeof window !== "undefined" && typeof sessionStorage !== "undefined";

// Safely access sessionStorage
const safeSessionStorage = {
  getItem: (key) => {
    try {
      return isBrowser ? sessionStorage.getItem(key) : null;
    } catch {
      return null;
    }
  },
  setItem: (key, value) => {
    try {
      if (isBrowser) sessionStorage.setItem(key, value);
    } catch {
      // Ignore storage errors
    }
  },
};

function PropertyTypeSelectComponent({ teamId, primaryColor }) {
  const [propertyTypes, setPropertyTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedPropertyType, setSelectedPropertyType] = useState("");

  // Handler for when a property type is selected
  const handlePropertyTypeChange = (e) => {
    const value = e.target.value;
    setSelectedPropertyType(value);

    // Set the global variable for the search button to use
    if (typeof window !== "undefined") {
      window.__selectedPropertyType = value;
    }
  };

  useEffect(() => {
    // Function to fetch properties and extract unique types
    const fetchPropertyTypes = async () => {
      // Skip if no teamId is provided
      if (!teamId) {
        return;
      }

      // Check if we have cached property types for this team
      const sessionStorageKey = `propertyTypes_${teamId}`;
      const cachedTypes = safeSessionStorage.getItem(sessionStorageKey);

      if (cachedTypes) {
        try {
          const parsedTypes = JSON.parse(cachedTypes);
          if (parsedTypes && parsedTypes.length > 0) {
            setPropertyTypes(parsedTypes);
            return;
          }
        } catch {
          // Continue with fetching if parsing fails
        }
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get the current URL to construct an absolute URL
        const currentUrl = new URL(window.location.href);
        const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
        const apiUrl = `${baseUrl}/api/get-team-property-types?teamId=${teamId}`;

        const response = await fetch(apiUrl);

        if (!response.ok) {
          throw new Error(`Failed to fetch property types: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.propertyTypes || !Array.isArray(data.propertyTypes)) {
          setPropertyTypes([]);
          setIsLoading(false);
          return;
        }

        const validTypes = data.propertyTypes.filter((type) => type);
        setPropertyTypes(validTypes);

        // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem(sessionStorageKey, JSON.stringify(validTypes));
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPropertyTypes();
  }, [teamId]);

  // Render select options based on state
  const renderSelectOptions = () => {
    if (isLoading) {
      return (
        <option value="" disabled>
          Carregando...
        </option>
      );
    }

    if (error) {
      return (
        <option value="" disabled>
          Erro ao carregar tipos
        </option>
      );
    }

    if (propertyTypes && propertyTypes.length > 0) {
      return propertyTypes.map((type) => (
        <option key={type} value={type}>
          {PROPERTY_TYPE_VALUE_LABEL_MAP[type] || type}
        </option>
      ));
    }

    // Always show at least one selectable option
    return (
      <option value="" disabled>
        Nenhum imóvel cadastrado
      </option>
    );
  };

  return (
    <div className="relative">
      <div className="pointer-events-none absolute inset-y-0 left-3 z-10 flex items-center">
        <Home11Icon className="h-6 w-6 opacity-50" style={{ color: primaryColor }} />
      </div>

      <div className="w-full">
        <Select
          style={{ "--tw-ring-color": primaryColor }}
          size="md"
          variant="ghost"
          className="w-full pr-4 pl-10"
          placeholder="Tipo de imóvel"
          value={selectedPropertyType}
          onChange={handlePropertyTypeChange}
          disabled={isLoading}
        >
          {renderSelectOptions()}
        </Select>
      </div>
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const PropertyTypeSelect = memo(PropertyTypeSelectComponent);
