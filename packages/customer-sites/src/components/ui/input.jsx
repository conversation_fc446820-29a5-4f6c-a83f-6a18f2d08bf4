import { forwardRef } from "react";
import { cn } from "../../utils/cn";

const sizeClasses = {
  xs: "input-xs text-base", // Ensure 16px font size on mobile to prevent zoom
  sm: "input-sm text-base", // Ensure 16px font size on mobile to prevent zoom
  md: "input-md text-base", // Ensure 16px font size on mobile to prevent zoom
  lg: "input-lg text-lg", // Already large enough
  xl: "input-xl text-xl", // Already large enough
};

const variantClasses = {
  ghost: "input-ghost !shadow-none !border-none !bg-transparent !outline-none",
  neutral: "input-neutral",
  primary: "input-primary",
  secondary: "input-secondary",
  accent: "input-accent",
  info: "input-info",
  success: "input-success",
  warning: "input-warning",
  error: "input-error",
};

const Input = forwardRef(
  (
    {
      className,
      size = "md",
      variant = "neutral",
      disabled = false,
      placeholder,
      value,
      defaultValue,
      onChange,
      type = "text",
      leftIcon,
      rightIcon,
      leftElement,
      rightElement,
      ...props
    },
    ref,
  ) => {
    const sizeClass = sizeClasses[size] || sizeClasses.md;
    const variantClass = variantClasses[variant] || variantClasses.neutral;

    const hasLeftContent = leftIcon || leftElement;
    const hasRightContent = rightIcon || rightElement;

    if (hasLeftContent || hasRightContent) {
      return (
        <label className={cn("input", sizeClass, variantClass, className)}>
          {hasLeftContent && (
            <div className="flex items-center justify-center opacity-50">{leftIcon || leftElement}</div>
          )}
          <input
            ref={ref}
            type={type}
            className="grow text-base" // Ensure 16px font size to prevent mobile zoom
            disabled={disabled}
            placeholder={placeholder}
            value={value}
            defaultValue={defaultValue}
            onChange={onChange}
            {...props}
          />
          {hasRightContent && (
            <div className="flex items-center justify-center opacity-50">{rightIcon || rightElement}</div>
          )}
        </label>
      );
    }

    const classes = cn("input", sizeClass, variantClass, className);

    return (
      <input
        ref={ref}
        type={type}
        className={classes}
        disabled={disabled}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        onChange={onChange}
        {...props}
      />
    );
  },
);

Input.displayName = "Input";

export { Input };
