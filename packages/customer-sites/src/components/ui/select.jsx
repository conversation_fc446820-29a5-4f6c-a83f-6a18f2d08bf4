import { forwardRef } from "react";
import { cn } from "../../utils/cn";

const sizeClasses = {
  xs: "select-xs",
  sm: "select-sm",
  md: "select-md",
  lg: "select-lg",
  xl: "select-xl",
};

const variantClasses = {
  ghost: "select-ghost",
  neutral: "select-neutral",
  primary: "select-primary",
  secondary: "select-secondary",
  accent: "select-accent",
  info: "select-info",
  success: "select-success",
  warning: "select-warning",
  error: "select-error",
};

const Select = forwardRef(
  (
    {
      children,
      className,
      size = "md",
      variant = "neutral",
      disabled = false,
      placeholder,
      value,
      defaultValue,
      onChange,
      ...props
    },
    ref,
  ) => {
    const sizeClass = sizeClasses[size] || sizeClasses.md;
    const variantClass = variantClasses[variant] || variantClasses.neutral;

    const classes = cn("select", sizeClass, variantClass, className);

    return (
      <select
        ref={ref}
        className={classes}
        disabled={disabled}
        value={value}
        defaultValue={defaultValue}
        onChange={onChange}
        {...props}
      >
        {placeholder && (
          <option value="" disabled={!defaultValue && !value}>
            {placeholder}
          </option>
        )}
        {children}
      </select>
    );
  },
);

Select.displayName = "Select";

export { Select };
