---
import "../styles/global.css";
// Import Poppins font from Fontsource
import "@fontsource/poppins/300.css"; // Light
import "@fontsource/poppins/400.css"; // Regular
import "@fontsource/poppins/500.css"; // Medium
import "@fontsource/poppins/600.css"; // SemiBold
import "@fontsource/poppins/700.css"; // Bold
import Footer from "../components/Footer.astro";
import PageHeader from "../components/PageHeader.astro";
import MobileMenu from "../components/MobileMenu.astro";
import Color from "colorjs.io";

// Read headers on the server
const headers = Astro.request.headers;
const pathPrefix = headers.get("x-original-path-prefix");

// Get website data from Astro.locals
const { websiteTheme, websiteInfo, teamProfile } = Astro.locals;

// Get the website address from URL params or fallback to default
const currentWebsiteAddress = Astro.params.websiteAddress || "default";

// Calculate background color (90% lighter version of primary_color)
let bgColor = "#f9f9f9"; // Default fallback color
if (websiteTheme?.primary_color) {
	try {
		// Create a Color object from the primary color
		const primaryColor = new Color(websiteTheme.primary_color);

		// Mix the color with white to make it lighter
		// 0.9 means 90% white, 10% original color
		const lightColor = Color.mix(primaryColor, "white", 0.99, {
			space: "lch",
		});

		// Convert to sRGB and get the hex string
		bgColor = lightColor.to("srgb").toString({ format: "hex" });
	} catch (error) {
		console.error("Error calculating background color:", error);
		// Keep the default fallback color if there's an error
	}
}
---

<html lang="pt-BR" data-theme="light">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{Astro.locals.teamProfile?.name}</title>

		{/* Swiper CSS */}
		<link
			rel="stylesheet"
			href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"
		/>

		{/* Preline UI CSS */}
		<link
			rel="stylesheet"
			href="https://cdn.jsdelivr.net/npm/preline@3.0.1/dist/preline.min.css"
		/>

		{
			import.meta.env.DEV && (
				<script is:inline src="/scripts/rewrite-links.js" />
			)
		}

		<slot name="head" />
	</head>
	<body style={`background-color: ${bgColor};`}>
		<div
			id="main-content"
			class="relative transition-transform duration-300 origin-center"
		>
			<PageHeader />
			<slot />
			<Footer />
		</div>

		<!-- Mobile menu component -->
		<MobileMenu />

		{/* Swiper JS */}
		<script
			is:inline
			src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"
		></script>

		{/* Preline UI JS */}
		<script
			is:inline
			src="https://cdn.jsdelivr.net/npm/preline@3.0.1/dist/preline.min.js"
		></script>
	</body>
</html>

<script
	is:inline
	define:vars={{
		pathPrefix,
		websiteTheme,
		websiteInfo,
		teamProfile,
		bgColor,
		currentWebsiteAddress,
	}}
>
	// Debug information
	console.log("Customer portal info (BaseLayout):", {
		pathPrefix,
		websiteTheme,
		websiteInfo,
		teamProfile,
		bgColor,
		currentWebsiteAddress,
	});

	// Make website info available globally
	window.websiteInfo = websiteInfo;
	window.websiteTheme = websiteTheme;
	window.teamProfile = teamProfile;
	window.websiteAddress = currentWebsiteAddress;

	// Mobile menu functionality
	document.addEventListener("DOMContentLoaded", () => {
		const mobileMenu = document.getElementById("mobile-menu");
		const mobileMenuPanel = document.getElementById("mobile-menu-panel");
		const mobileMenuOverlay = document.getElementById(
			"mobile-menu-overlay",
		);
		const mobileMenuClose = document.getElementById("mobile-menu-close");
		const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
		const mainContent = document.getElementById("main-content");

		console.log("Mobile menu elements:", {
			mobileMenu,
			mobileMenuPanel,
			mobileMenuOverlay,
			mobileMenuClose,
			mobileMenuToggle,
			mainContent,
		});

		// Function to open the mobile menu
		function openMobileMenu() {
			console.log("Opening mobile menu");
			if (
				!mobileMenu ||
				!mobileMenuPanel ||
				!mobileMenuOverlay ||
				!mainContent
			) {
				console.error("Missing mobile menu elements");
				return;
			}

			// Make menu visible and interactive
			mobileMenu.classList.add("pointer-events-auto");
			mobileMenu.classList.remove("pointer-events-none");

			// Show the overlay with fade effect
			mobileMenuOverlay.classList.remove(
				"bg-black/0",
				"pointer-events-none",
			);
			mobileMenuOverlay.classList.add(
				"bg-black/50",
				"pointer-events-auto",
			);

			// Slide in the menu panel
			mobileMenuPanel.classList.remove("translate-x-full", "opacity-0");
			mobileMenuPanel.classList.add("translate-x-0", "opacity-100");

			// Apply transform to main content (push and tilt effect)
			mainContent.classList.add("transform-active");

			// Prevent body scrolling
			document.body.style.overflow = "hidden";
		}

		// Function to close the mobile menu
		function closeMobileMenu() {
			console.log("Closing mobile menu");
			if (
				!mobileMenu ||
				!mobileMenuPanel ||
				!mobileMenuOverlay ||
				!mainContent
			) {
				console.error("Missing mobile menu elements");
				return;
			}

			// Hide the overlay
			mobileMenuOverlay.classList.add(
				"bg-black/0",
				"pointer-events-none",
			);
			mobileMenuOverlay.classList.remove(
				"bg-black/50",
				"pointer-events-auto",
			);

			// Slide out the menu panel
			mobileMenuPanel.classList.add("translate-x-full", "opacity-0");
			mobileMenuPanel.classList.remove("translate-x-0", "opacity-100");

			// Remove transform from main content
			mainContent.classList.remove("transform-active");

			// After animation completes, make menu non-interactive
			setTimeout(() => {
				mobileMenu.classList.remove("pointer-events-auto");
				mobileMenu.classList.add("pointer-events-none");

				// Restore body scrolling
				document.body.style.overflow = "";
			}, 300); // Match the transition duration
		}

		// Event listeners
		if (mobileMenuToggle) {
			console.log("Adding click listener to mobile menu toggle");
			mobileMenuToggle.addEventListener("click", openMobileMenu);
		}

		if (mobileMenuClose) {
			mobileMenuClose.addEventListener("click", closeMobileMenu);
		}

		if (mobileMenuOverlay) {
			mobileMenuOverlay.addEventListener("click", closeMobileMenu);
		}

		// Close menu on escape key
		document.addEventListener("keydown", (e) => {
			if (e.key === "Escape") {
				closeMobileMenu();
			}
		});
	});
</script>
