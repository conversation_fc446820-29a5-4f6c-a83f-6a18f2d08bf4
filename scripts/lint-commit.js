#!/usr/bin/env node

const fs = require("node:fs");
const { execSync } = require("node:child_process");

const BIOME_CONFIG = "biome.json";
const BACKUP_CONFIG = "biome.json.backup";

function main() {
  try {
    // Read the current biome.json
    const configContent = fs.readFileSync(BIOME_CONFIG, "utf8");
    const config = JSON.parse(configContent);

    // Create backup
    fs.writeFileSync(BACKUP_CONFIG, configContent);

    // Modify the config to enable unsafe fixes for unused variables and imports
    if (config.linter?.rules?.correctness) {
      if (config.linter.rules.correctness.noUnusedVariables) {
        config.linter.rules.correctness.noUnusedVariables.fix = "unsafe";
      }
      if (config.linter.rules.correctness.noUnusedImports) {
        config.linter.rules.correctness.noUnusedImports.fix = "unsafe";
      }
    }

    // Write the modified config
    fs.writeFileSync(BIOME_CONFIG, JSON.stringify(config, null, 2));

    // Run biome check with the modified config
    const files = process.argv.slice(2);
    if (files.length > 0) {
      execSync(`biome check --write --unsafe ${files.join(" ")}`, { stdio: "inherit" });
    } else {
      execSync("biome check --write --unsafe", { stdio: "inherit" });
    }
  } catch (error) {
    console.error("Error during commit linting:", error.message);
    process.exit(1);
  } finally {
    // Always restore the original config
    if (fs.existsSync(BACKUP_CONFIG)) {
      fs.renameSync(BACKUP_CONFIG, BIOME_CONFIG);
    }
  }
}

main();
